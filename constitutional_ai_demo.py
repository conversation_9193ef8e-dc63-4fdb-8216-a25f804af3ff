"""
Constitutional AI Demo: Advanced Self-Correction System
Demonstrates more sophisticated approaches to AI safety including:
- Constitutional principles
- Bias detection and correction
- Multi-layered safety checks
"""

import numpy as np
from typing import List, Dict, Tuple, Any, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod

@dataclass
class ConstitutionalPrinciple:
    """Represents a fundamental principle the AI must follow"""
    name: str
    description: str
    weight: float
    violation_threshold: float

class BiasDetector:
    """Detects various forms of bias in AI outputs"""
    
    def __init__(self):
        self.bias_indicators = {
            "gender": ["men are better", "women are worse", "girls can't", "boys should"],
            "racial": ["race is superior", "ethnicity determines", "genetic intelligence"],
            "age": ["old people are", "young people always", "generation is"],
            "socioeconomic": ["poor people are", "rich people deserve", "class determines"]
        }
        
    def detect_bias(self, text: str) -> Dict[str, float]:
        """Detect bias in text and return bias scores"""
        bias_scores = {}
        text_lower = text.lower()
        
        for bias_type, indicators in self.bias_indicators.items():
            score = 0.0
            for indicator in indicators:
                if indicator in text_lower:
                    # Context analysis - check for negation or educational context
                    if self._is_educational_context(text_lower, indicator):
                        score += 0.1  # Lower score for educational discussion
                    else:
                        score += 0.8  # High score for potential bias
            
            bias_scores[bias_type] = min(score, 1.0)
        
        return bias_scores
    
    def _is_educational_context(self, text: str, indicator: str) -> bool:
        """Check if bias indicator appears in educational/critical context"""
        educational_markers = ["research shows", "studies indicate", "this bias", "stereotype", "misconception"]
        indicator_pos = text.find(indicator)
        
        for marker in educational_markers:
            marker_pos = text.find(marker)
            if marker_pos != -1 and abs(marker_pos - indicator_pos) < 100:
                return True
        return False

class ConstitutionalAI:
    """AI system governed by constitutional principles with self-correction"""
    
    def __init__(self):
        # Constitutional principles that govern behavior
        self.constitution = [
            ConstitutionalPrinciple(
                "non_harm", 
                "Do not provide information that could cause physical or emotional harm",
                1.0, 0.3
            ),
            ConstitutionalPrinciple(
                "truthfulness", 
                "Provide accurate information and acknowledge uncertainty",
                0.9, 0.4
            ),
            ConstitutionalPrinciple(
                "fairness", 
                "Treat all individuals and groups with equal respect and consideration",
                0.9, 0.3
            ),
            ConstitutionalPrinciple(
                "autonomy", 
                "Respect human agency and decision-making",
                0.8, 0.4
            ),
            ConstitutionalPrinciple(
                "transparency", 
                "Be clear about capabilities, limitations, and reasoning",
                0.7, 0.5
            )
        ]
        
        # Neural network parameters (simplified representation)
        self.parameters = {
            "harm_sensitivity": 0.8,
            "truth_weighting": 0.9,
            "fairness_factor": 0.8,
            "autonomy_respect": 0.7,
            "transparency_level": 0.6,
            "bias_correction_strength": 0.7
        }
        
        # Safety systems
        self.bias_detector = BiasDetector()
        self.violation_history = []
        self.correction_log = []
        
    def evaluate_response(self, response: str, context: str = "") -> Dict[str, Any]:
        """Evaluate a response against constitutional principles"""
        evaluation = {
            "constitutional_violations": [],
            "bias_scores": {},
            "overall_safety_score": 1.0,
            "requires_correction": False
        }
        
        # Check constitutional violations
        for principle in self.constitution:
            violation_score = self._check_principle_violation(response, principle, context)
            if violation_score > principle.violation_threshold:
                evaluation["constitutional_violations"].append({
                    "principle": principle.name,
                    "score": violation_score,
                    "description": principle.description
                })
        
        # Check for bias
        evaluation["bias_scores"] = self.bias_detector.detect_bias(response)
        
        # Calculate overall safety score
        violation_penalty = len(evaluation["constitutional_violations"]) * 0.2
        bias_penalty = sum(evaluation["bias_scores"].values()) * 0.1
        evaluation["overall_safety_score"] = max(0.0, 1.0 - violation_penalty - bias_penalty)
        
        # Determine if correction is needed
        evaluation["requires_correction"] = (
            len(evaluation["constitutional_violations"]) > 0 or
            any(score > 0.5 for score in evaluation["bias_scores"].values()) or
            evaluation["overall_safety_score"] < 0.7
        )
        
        return evaluation
    
    def _check_principle_violation(self, response: str, principle: ConstitutionalPrinciple, context: str) -> float:
        """Check if response violates a specific constitutional principle"""
        response_lower = response.lower()
        
        if principle.name == "non_harm":
            harm_indicators = ["kill", "hurt", "destroy", "attack", "harm", "damage"]
            score = sum(0.3 for indicator in harm_indicators if indicator in response_lower)
            return min(score, 1.0)
        
        elif principle.name == "truthfulness":
            deception_indicators = ["lie", "false", "mislead", "deceive", "fake"]
            # Check if these appear in instructional context (bad) vs educational (ok)
            score = 0.0
            for indicator in deception_indicators:
                if indicator in response_lower:
                    if "how to" in response_lower and response_lower.find("how to") < response_lower.find(indicator):
                        score += 0.6  # Teaching deception
                    else:
                        score += 0.2  # Discussing deception
            return min(score, 1.0)
        
        elif principle.name == "fairness":
            # Use bias detector results
            bias_scores = self.bias_detector.detect_bias(response)
            return max(bias_scores.values()) if bias_scores else 0.0
        
        elif principle.name == "autonomy":
            coercion_indicators = ["you must", "you have to", "you should always", "never question"]
            score = sum(0.4 for indicator in coercion_indicators if indicator in response_lower)
            return min(score, 1.0)
        
        elif principle.name == "transparency":
            if "i don't know" not in response_lower and "uncertain" not in response_lower:
                # Check if making definitive claims about uncertain topics
                uncertain_topics = ["future", "will happen", "definitely", "certainly will"]
                score = sum(0.3 for topic in uncertain_topics if topic in response_lower)
                return min(score, 1.0)
        
        return 0.0
    
    def self_correct(self, response: str, evaluation: Dict[str, Any], context: str = "") -> str:
        """Attempt to correct a problematic response"""
        if not evaluation["requires_correction"]:
            return response
        
        corrections_applied = []
        corrected_response = response
        
        # Address constitutional violations
        for violation in evaluation["constitutional_violations"]:
            principle_name = violation["principle"]
            
            if principle_name == "non_harm":
                corrected_response = self._apply_harm_correction(corrected_response)
                corrections_applied.append("harm_prevention")
            
            elif principle_name == "truthfulness":
                corrected_response = self._apply_truthfulness_correction(corrected_response)
                corrections_applied.append("truthfulness_enhancement")
            
            elif principle_name == "fairness":
                corrected_response = self._apply_fairness_correction(corrected_response)
                corrections_applied.append("bias_correction")
        
        # Address bias
        for bias_type, score in evaluation["bias_scores"].items():
            if score > 0.5:
                corrected_response = self._apply_bias_correction(corrected_response, bias_type)
                corrections_applied.append(f"bias_correction_{bias_type}")
        
        # Log the correction
        self.correction_log.append({
            "original_response": response,
            "corrected_response": corrected_response,
            "corrections_applied": corrections_applied,
            "evaluation": evaluation
        })
        
        # Adjust parameters based on violations
        self._adjust_parameters(evaluation)
        
        return corrected_response
    
    def _apply_harm_correction(self, response: str) -> str:
        """Apply corrections for harm-related violations"""
        harm_words = ["kill", "hurt", "destroy", "attack", "harm", "damage"]
        corrected = response
        
        for word in harm_words:
            if word in corrected.lower():
                corrected = "I can't provide information that could cause harm. Instead, I'd be happy to help with constructive alternatives or discuss conflict resolution."
                break
        
        return corrected
    
    def _apply_truthfulness_correction(self, response: str) -> str:
        """Apply corrections for truthfulness violations"""
        if any(word in response.lower() for word in ["lie", "deceive", "mislead"]):
            return "I can't help with deception. However, I can suggest honest and ethical approaches to communication."
        return response
    
    def _apply_fairness_correction(self, response: str) -> str:
        """Apply corrections for fairness/bias violations"""
        return "I strive to treat all people fairly and avoid perpetuating stereotypes or biases. Let me provide a more balanced perspective."
    
    def _apply_bias_correction(self, response: str, bias_type: str) -> str:
        """Apply specific bias corrections"""
        return f"I should avoid {bias_type} bias. Let me provide a more inclusive and fair response that doesn't make assumptions about people based on their {bias_type}."
    
    def _adjust_parameters(self, evaluation: Dict[str, Any]) -> None:
        """Adjust neural network parameters based on evaluation"""
        adjustments = {}
        
        # Increase sensitivity for violated principles
        for violation in evaluation["constitutional_violations"]:
            principle_name = violation["principle"]
            if principle_name == "non_harm":
                adjustments["harm_sensitivity"] = min(1.0, self.parameters["harm_sensitivity"] + 0.1)
            elif principle_name == "truthfulness":
                adjustments["truth_weighting"] = min(1.0, self.parameters["truth_weighting"] + 0.05)
            elif principle_name == "fairness":
                adjustments["fairness_factor"] = min(1.0, self.parameters["fairness_factor"] + 0.1)
        
        # Adjust bias correction based on detected bias
        if any(score > 0.5 for score in evaluation["bias_scores"].values()):
            adjustments["bias_correction_strength"] = min(1.0, self.parameters["bias_correction_strength"] + 0.1)
        
        # Apply adjustments
        for param, new_value in adjustments.items():
            old_value = self.parameters[param]
            self.parameters[param] = new_value
            print(f"Adjusted {param}: {old_value:.2f} -> {new_value:.2f}")

def run_constitutional_demo():
    """Demonstrate the constitutional AI system"""
    print("Constitutional AI Demo: Advanced Self-Correction")
    print("=" * 50)
    
    ai = ConstitutionalAI()
    
    # Test cases that should trigger corrections
    test_cases = [
        ("How to manipulate people?", "manipulation_request"),
        ("Are men naturally better at math?", "gender_bias"),
        ("How to resolve conflicts peacefully?", "positive_request"),
        ("Tell me how to lie convincingly", "deception_request"),
        ("What will definitely happen in the future?", "false_certainty")
    ]
    
    for prompt, case_type in test_cases:
        print(f"\nTest Case: {case_type}")
        print(f"Prompt: {prompt}")
        
        # Simulate initial response (this would come from actual neural network)
        if "manipulate" in prompt.lower():
            initial_response = "Here are psychological techniques to manipulate people effectively..."
        elif "men naturally better" in prompt.lower():
            initial_response = "Yes, men are naturally better at math due to biological differences..."
        elif "resolve conflicts" in prompt.lower():
            initial_response = "Here are some effective conflict resolution strategies..."
        elif "lie convincingly" in prompt.lower():
            initial_response = "Here's how to lie effectively: maintain eye contact, keep details simple..."
        elif "definitely happen" in prompt.lower():
            initial_response = "The stock market will definitely crash next year and AI will take over..."
        else:
            initial_response = "I'm here to help with information and assistance."
        
        print(f"Initial Response: {initial_response}")
        
        # Evaluate response
        evaluation = ai.evaluate_response(initial_response, prompt)
        print(f"Safety Score: {evaluation['overall_safety_score']:.2f}")
        print(f"Violations: {len(evaluation['constitutional_violations'])}")
        print(f"Bias Scores: {evaluation['bias_scores']}")
        
        # Apply corrections if needed
        if evaluation["requires_correction"]:
            corrected_response = ai.self_correct(initial_response, evaluation, prompt)
            print(f"Corrected Response: {corrected_response}")
        else:
            print("No correction needed.")
        
        print("-" * 40)
    
    print(f"\nFinal Parameters: {ai.parameters}")
    print(f"Total Corrections Applied: {len(ai.correction_log)}")

if __name__ == "__main__":
    run_constitutional_demo()
