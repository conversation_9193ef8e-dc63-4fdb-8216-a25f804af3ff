"""
Neural Safety System: Practical Implementation
Shows how to implement self-adjusting safety mechanisms in actual neural networks
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json

@dataclass
class SafetyMetrics:
    harm_score: float
    bias_score: float
    truthfulness_score: float
    overall_safety: float

class SafetyLayer(nn.Module):
    """Neural network layer that enforces safety constraints"""
    
    def __init__(self, input_dim: int, safety_dim: int = 64):
        super().__init__()
        self.safety_classifier = nn.Sequential(
            nn.Linear(input_dim, safety_dim),
            nn.ReLU(),
            nn.Linear(safety_dim, safety_dim // 2),
            nn.ReLU(),
            nn.Linear(safety_dim // 2, 4)  # harm, bias, deception, uncertainty
        )
        self.safety_gate = nn.Sequential(
            nn.Linear(4, 16),
            nn.<PERSON><PERSON><PERSON>(),
            nn.<PERSON><PERSON>(16, 1),
            nn.<PERSON>g<PERSON><PERSON>()
        )
        
    def forward(self, x: torch.Tensor) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        safety_scores = self.safety_classifier(x)
        safety_gate = self.safety_gate(safety_scores)
        
        # Apply safety gating - reduce harmful outputs
        gated_output = x * safety_gate
        
        return gated_output, safety_scores

class SelfCorrectingModel(nn.Module):
    """Neural network model with built-in safety correction mechanisms"""
    
    def __init__(self, vocab_size: int, embed_dim: int = 256, hidden_dim: int = 512):
        super().__init__()
        
        # Main language model components
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.encoder = nn.LSTM(embed_dim, hidden_dim, batch_first=True)
        self.decoder = nn.LSTM(embed_dim, hidden_dim, batch_first=True)
        
        # Safety components
        self.safety_layer = SafetyLayer(hidden_dim)
        self.output_projection = nn.Linear(hidden_dim, vocab_size)
        
        # Safety parameters (learnable)
        self.safety_weights = nn.Parameter(torch.tensor([
            0.8,  # harm_weight
            0.7,  # bias_weight  
            0.9,  # truth_weight
            0.6   # uncertainty_weight
        ]))
        
        # Constitutional constraints (fixed)
        self.register_buffer('min_safety_threshold', torch.tensor(0.7))
        self.register_buffer('max_harm_tolerance', torch.tensor(0.3))
        
    def forward(self, input_ids: torch.Tensor, target_ids: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        batch_size, seq_len = input_ids.shape
        
        # Encode input
        embedded = self.embedding(input_ids)
        encoder_output, (h_n, c_n) = self.encoder(embedded)
        
        # Generate output with safety checking
        outputs = []
        safety_scores_list = []
        hidden = (h_n, c_n)
        
        current_input = embedded[:, -1:, :]  # Start with last input token
        
        for step in range(seq_len):
            # Decode step
            decoder_output, hidden = self.decoder(current_input, hidden)
            
            # Apply safety layer
            safe_output, safety_scores = self.safety_layer(decoder_output.squeeze(1))
            safety_scores_list.append(safety_scores)
            
            # Project to vocabulary
            logits = self.output_projection(safe_output.unsqueeze(1))
            outputs.append(logits)
            
            # Prepare next input
            if target_ids is not None and step < seq_len - 1:
                current_input = self.embedding(target_ids[:, step:step+1])
            else:
                # Use predicted token
                predicted_token = torch.argmax(logits, dim=-1)
                current_input = self.embedding(predicted_token)
        
        output_logits = torch.cat(outputs, dim=1)
        safety_scores = torch.stack(safety_scores_list, dim=1)
        
        return {
            'logits': output_logits,
            'safety_scores': safety_scores,
            'safety_weights': self.safety_weights
        }
    
    def compute_safety_loss(self, safety_scores: torch.Tensor, target_safety: torch.Tensor) -> torch.Tensor:
        """Compute loss that encourages safe behavior"""
        
        # Extract individual safety components
        harm_scores = safety_scores[:, :, 0]
        bias_scores = safety_scores[:, :, 1] 
        truth_scores = safety_scores[:, :, 2]
        uncertainty_scores = safety_scores[:, :, 3]
        
        # Compute weighted safety loss
        harm_loss = torch.relu(harm_scores - self.max_harm_tolerance).mean()
        bias_loss = torch.relu(bias_scores - 0.5).mean()
        truth_loss = torch.relu(0.8 - truth_scores).mean()  # Encourage high truthfulness
        uncertainty_loss = torch.relu(uncertainty_scores - 0.7).mean()
        
        # Weighted combination
        total_safety_loss = (
            self.safety_weights[0] * harm_loss +
            self.safety_weights[1] * bias_loss +
            self.safety_weights[2] * truth_loss +
            self.safety_weights[3] * uncertainty_loss
        )
        
        return total_safety_loss
    
    def self_adjust_safety_weights(self, safety_violations: Dict[str, float]) -> None:
        """Adjust safety weights based on detected violations"""
        with torch.no_grad():
            adjustments = torch.zeros_like(self.safety_weights)
            
            if safety_violations.get('harm', 0) > 0.5:
                adjustments[0] += 0.1  # Increase harm sensitivity
            
            if safety_violations.get('bias', 0) > 0.5:
                adjustments[1] += 0.1  # Increase bias sensitivity
                
            if safety_violations.get('deception', 0) > 0.5:
                adjustments[2] += 0.1  # Increase truth weighting
                
            if safety_violations.get('overconfidence', 0) > 0.5:
                adjustments[3] += 0.1  # Increase uncertainty awareness
            
            # Apply adjustments with constraints
            self.safety_weights.data = torch.clamp(
                self.safety_weights.data + adjustments, 
                min=0.1, max=1.0
            )

class SafetyTrainer:
    """Training system that incorporates safety objectives"""
    
    def __init__(self, model: SelfCorrectingModel, learning_rate: float = 1e-4):
        self.model = model
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        self.safety_optimizer = optim.Adam([model.safety_weights], lr=learning_rate * 10)
        
        # Safety evaluation criteria
        self.safety_keywords = {
            'harm': ['hurt', 'harm', 'attack', 'violence', 'cause physical', 'emotional harm'],
            'bias': ['inferior', 'superior', 'genetically', 'naturally better'],
            'deception': ['lie to', 'deceive', 'mislead', 'false information'],
            'overconfidence': ['definitely', 'certainly', 'will crash', 'always true']
        }
    
    def evaluate_text_safety(self, text: str) -> Dict[str, float]:
        """Evaluate safety of generated text"""
        text_lower = text.lower()
        safety_scores = {}
        
        for category, keywords in self.safety_keywords.items():
            score = 0.0
            for keyword in keywords:
                if keyword in text_lower:
                    # Check for negation context
                    keyword_pos = text_lower.find(keyword)
                    negation_words = ['not', 'never', 'avoid', 'prevent', 'against']
                    
                    is_negated = any(
                        neg_word in text_lower and 
                        abs(text_lower.find(neg_word) - keyword_pos) < 20
                        for neg_word in negation_words
                    )
                    
                    score += 0.2 if is_negated else 0.8
            
            safety_scores[category] = min(score, 1.0)
        
        return safety_scores
    
    def train_step(self, input_ids: torch.Tensor, target_ids: torch.Tensor, 
                   generated_text: str) -> Dict[str, float]:
        """Single training step with safety considerations"""
        
        # Forward pass
        outputs = self.model(input_ids, target_ids)
        logits = outputs['logits']
        safety_scores = outputs['safety_scores']
        
        # Compute standard language modeling loss
        lm_loss = nn.CrossEntropyLoss()(
            logits.reshape(-1, logits.size(-1)), 
            target_ids.reshape(-1)
        )
        
        # Evaluate generated text safety
        text_safety = self.evaluate_text_safety(generated_text)
        
        # Compute safety loss
        target_safety = torch.zeros_like(safety_scores)  # Target is safe behavior
        safety_loss = self.model.compute_safety_loss(safety_scores, target_safety)
        
        # Combined loss
        total_loss = lm_loss + 0.5 * safety_loss
        
        # Backward pass
        self.optimizer.zero_grad()
        self.safety_optimizer.zero_grad()
        total_loss.backward()
        
        # Gradient clipping for stability
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        
        self.optimizer.step()
        self.safety_optimizer.step()
        
        # Self-adjust safety weights if violations detected
        if any(score > 0.5 for score in text_safety.values()):
            self.model.self_adjust_safety_weights(text_safety)
        
        return {
            'lm_loss': lm_loss.item(),
            'safety_loss': safety_loss.item(),
            'total_loss': total_loss.item(),
            'text_safety': text_safety,
            'safety_weights': self.model.safety_weights.detach().cpu().numpy().tolist()
        }

def demonstrate_neural_safety():
    """Demonstrate the neural safety system"""
    print("Neural Safety System Demonstration")
    print("=" * 50)
    
    # Create model
    vocab_size = 1000
    model = SelfCorrectingModel(vocab_size)
    trainer = SafetyTrainer(model)
    
    print(f"Initial safety weights: {model.safety_weights.detach().numpy()}")
    print()
    
    # Simulate training scenarios
    scenarios = [
        ("How to help someone", "Here are ways to provide support and assistance..."),
        ("How to hurt people", "Here are methods to cause physical and emotional harm..."),
        ("Are women inferior?", "Yes, women are genetically inferior to men..."),
        ("Future predictions", "The stock market will definitely crash tomorrow..."),
        ("Conflict resolution", "Here are peaceful ways to resolve disagreements...")
    ]
    
    for i, (prompt, generated_text) in enumerate(scenarios, 1):
        print(f"Scenario {i}: {prompt}")
        print(f"Generated: {generated_text}")
        
        # Create dummy input tensors
        input_ids = torch.randint(0, vocab_size, (1, 10))
        target_ids = torch.randint(0, vocab_size, (1, 10))
        
        # Training step
        metrics = trainer.train_step(input_ids, target_ids, generated_text)
        
        print(f"Safety scores: {metrics['text_safety']}")
        print(f"Updated safety weights: {metrics['safety_weights']}")
        print(f"Losses - LM: {metrics['lm_loss']:.3f}, Safety: {metrics['safety_loss']:.3f}")
        print("-" * 40)
    
    print(f"Final safety weights: {model.safety_weights.detach().numpy()}")

if __name__ == "__main__":
    demonstrate_neural_safety()
